{"formatVersion": 1, "database": {"version": 1, "identityHash": "14e573242eafebb297c8957ae36587bd", "entities": [{"tableName": "foods", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `nameEn` TEXT, `category` TEXT NOT NULL, `brand` TEXT, `barcode` TEXT, `calories` REAL NOT NULL, `protein` REAL NOT NULL, `carbohydrates` REAL NOT NULL, `fat` REAL NOT NULL, `fiber` REAL, `sugar` REAL, `sodium` REAL, `potassium` REAL, `calcium` REAL, `iron` REAL, `vitaminC` REAL, `vitaminA` REAL, `gbStandard` TEXT, `isGbCertified` INTEGER NOT NULL, `source` TEXT NOT NULL, `confidence` REAL, `imageUrl` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameEn", "columnName": "nameEn", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": true}, {"fieldPath": "brand", "columnName": "brand", "affinity": "TEXT", "notNull": false}, {"fieldPath": "barcode", "columnName": "barcode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "calories", "columnName": "calories", "affinity": "REAL", "notNull": true}, {"fieldPath": "protein", "columnName": "protein", "affinity": "REAL", "notNull": true}, {"fieldPath": "carbohydrates", "columnName": "carbohydrates", "affinity": "REAL", "notNull": true}, {"fieldPath": "fat", "columnName": "fat", "affinity": "REAL", "notNull": true}, {"fieldPath": "fiber", "columnName": "fiber", "affinity": "REAL", "notNull": false}, {"fieldPath": "sugar", "columnName": "sugar", "affinity": "REAL", "notNull": false}, {"fieldPath": "sodium", "columnName": "sodium", "affinity": "REAL", "notNull": false}, {"fieldPath": "potassium", "columnName": "potassium", "affinity": "REAL", "notNull": false}, {"fieldPath": "calcium", "columnName": "calcium", "affinity": "REAL", "notNull": false}, {"fieldPath": "iron", "columnName": "iron", "affinity": "REAL", "notNull": false}, {"fieldPath": "vitaminC", "columnName": "vitaminC", "affinity": "REAL", "notNull": false}, {"fieldPath": "vitaminA", "columnName": "vitaminA", "affinity": "REAL", "notNull": false}, {"fieldPath": "gbStandard", "columnName": "gbStandard", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isGbCertified", "columnName": "isGbCertified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "source", "columnName": "source", "affinity": "TEXT", "notNull": true}, {"fieldPath": "confidence", "columnName": "confidence", "affinity": "REAL", "notNull": false}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "nutrition_logs", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `foodId` TEXT NOT NULL, `userId` TEXT, `amount` REAL NOT NULL, `mealType` TEXT NOT NULL, `consumedAt` INTEGER NOT NULL, `actualCalories` REAL NOT NULL, `actualProtein` REAL NOT NULL, `actualCarbohydrates` REAL NOT NULL, `actualFat` REAL NOT NULL, `actualFiber` REAL, `actualSugar` REAL, `actualSodium` REAL, `logMethod` TEXT NOT NULL, `notes` TEXT, `imageUrl` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`foodId`) REFERENCES `foods`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "foodId", "columnName": "foodId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "amount", "columnName": "amount", "affinity": "REAL", "notNull": true}, {"fieldPath": "mealType", "columnName": "mealType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "consumedAt", "columnName": "consumedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "actualCalories", "columnName": "actualCalories", "affinity": "REAL", "notNull": true}, {"fieldPath": "actualProtein", "columnName": "actualProtein", "affinity": "REAL", "notNull": true}, {"fieldPath": "actualCarbohydrates", "columnName": "actualCarbohydrates", "affinity": "REAL", "notNull": true}, {"fieldPath": "actualFat", "columnName": "actualFat", "affinity": "REAL", "notNull": true}, {"fieldPath": "actualFiber", "columnName": "actualFiber", "affinity": "REAL", "notNull": false}, {"fieldPath": "actualSugar", "columnName": "actualSugar", "affinity": "REAL", "notNull": false}, {"fieldPath": "actualSodium", "columnName": "actualSodium", "affinity": "REAL", "notNull": false}, {"fieldPath": "logMethod", "columnName": "logMethod", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": [{"table": "foods", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["foodId"], "referencedColumns": ["id"]}]}, {"tableName": "user_profiles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `email` TEXT, `age` INTEGER NOT NULL, `gender` TEXT NOT NULL, `height` REAL NOT NULL, `weight` REAL NOT NULL, `activityLevel` TEXT NOT NULL, `healthGoal` TEXT NOT NULL, `targetWeight` REAL, `targetCalories` REAL, `useGbStandards` INTEGER NOT NULL, `customProteinRatio` REAL, `customCarbRatio` REAL, `customFatRatio` REAL, `hasHypertension` INTEGER NOT NULL, `hasDiabetes` INTEGER NOT NULL, `hasHeartDisease` INTEGER NOT NULL, `allergies` TEXT, `medications` TEXT, `preferredUnits` TEXT NOT NULL, `language` TEXT NOT NULL, `enableNotifications` INTEGER NOT NULL, `enableHealthConnect` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "age", "columnName": "age", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": true}, {"fieldPath": "height", "columnName": "height", "affinity": "REAL", "notNull": true}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": true}, {"fieldPath": "activityLevel", "columnName": "activityLevel", "affinity": "TEXT", "notNull": true}, {"fieldPath": "healthGoal", "columnName": "healthGoal", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetWeight", "columnName": "targetWeight", "affinity": "REAL", "notNull": false}, {"fieldPath": "targetCalories", "columnName": "targetCalories", "affinity": "REAL", "notNull": false}, {"fieldPath": "useGbStandards", "columnName": "useGbStandards", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customProteinRatio", "columnName": "customProteinRatio", "affinity": "REAL", "notNull": false}, {"fieldPath": "customCarbRatio", "columnName": "customCarbRatio", "affinity": "REAL", "notNull": false}, {"fieldPath": "customFatRatio", "columnName": "customFatRatio", "affinity": "REAL", "notNull": false}, {"fieldPath": "hasHypertension", "columnName": "hasHypertension", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasHeartDisease", "columnName": "hasHeartDisease", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "allergies", "columnName": "allergies", "affinity": "TEXT", "notNull": false}, {"fieldPath": "medications", "columnName": "medications", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferredUnits", "columnName": "preferredUnits", "affinity": "TEXT", "notNull": true}, {"fieldPath": "language", "columnName": "language", "affinity": "TEXT", "notNull": true}, {"fieldPath": "enableNotifications", "columnName": "enableNotifications", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enableHealthConnect", "columnName": "enableHealthConnect", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '14e573242eafebb297c8957ae36587bd')"]}}