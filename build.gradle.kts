// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id("com.android.application") version "8.2.0" apply false
    id("org.jetbrains.kotlin.android") version "1.9.20" apply false
    id("com.google.dagger.hilt.android") version "2.48" apply false
    id("com.google.devtools.ksp") version "1.9.20-1.0.14" apply false
    id("androidx.room") version "2.6.1" apply false
}

buildscript {
    extra.apply {
        set("compose_version", "2023.10.01")
        set("compose_compiler_version", "1.5.5")
        set("kotlin_version", "1.9.20")
        set("hilt_version", "2.48")
        set("room_version", "2.6.1")
        set("camerax_version", "1.3.1")
        set("health_connect_version", "1.1.0-alpha07")
    }
}
